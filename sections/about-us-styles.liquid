{{ 'section-about-us.css' | asset_url | stylesheet_tag }}

<script>
// Professional About Us Page Enhancement
document.addEventListener('DOMContentLoaded', function() {
  // Add classes to existing rich text sections for better styling
  const richTextSections = document.querySelectorAll('.rich-text');

  richTextSections.forEach(function(section, index) {
    if (index === 0) {
      section.classList.add('about-hero');
    } else if (index === 1) {
      section.classList.add('about-content');
    }
  });

  // Add professional styling to contact form section
  const contactSection = document.querySelector('.contact-form');
  if (contactSection) {
    contactSection.classList.add('contact-form-section');
  }

  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        entry.target.classList.add('revealed');
      }
    });
  }, observerOptions);

  // Add scroll reveal to elements
  const elementsToReveal = document.querySelectorAll('.about-content h1, .about-content p, .services-grid, .service-card, .stats-grid');
  elementsToReveal.forEach(function(el) {
    el.classList.add('scroll-reveal');
    observer.observe(el);
  });

  // Animate statistics numbers when they come into view
  const statNumbers = document.querySelectorAll('.stat-number');
  statNumbers.forEach(function(statEl) {
    const observer = new IntersectionObserver(function(entries) {
      entries.forEach(function(entry) {
        if (entry.isIntersecting) {
          animateNumber(entry.target);
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.5 });

    observer.observe(statEl);
  });

  // Number animation function
  function animateNumber(element) {
    const finalNumber = element.textContent;
    const isPercentage = finalNumber.includes('%');
    const isPlusSign = finalNumber.includes('+');
    const numericValue = parseInt(finalNumber.replace(/[^\d]/g, ''));

    let currentNumber = 0;
    const increment = numericValue / 50; // 50 steps for smooth animation

    const timer = setInterval(function() {
      currentNumber += increment;
      if (currentNumber >= numericValue) {
        currentNumber = numericValue;
        clearInterval(timer);
      }

      let displayValue = Math.floor(currentNumber).toString();
      if (isPlusSign) displayValue += '+';
      if (isPercentage) displayValue += '%';

      element.textContent = displayValue;
    }, 30);
  }

  // Add click handlers to service cards for better UX
  const serviceCards = document.querySelectorAll('.service-card');
  serviceCards.forEach(function(card) {
    card.addEventListener('click', function() {
      // Add a subtle click effect
      this.style.transform = 'scale(0.98)';
      setTimeout(() => {
        this.style.transform = '';
      }, 150);
    });

    // Add keyboard accessibility
    card.setAttribute('tabindex', '0');
    card.addEventListener('keydown', function(e) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        this.click();
      }
    });
  });

  // Smooth scrolling for any internal links
  const internalLinks = document.querySelectorAll('a[href^="#"]');
  internalLinks.forEach(function(link) {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
});
</script>

{% schema %}
{
  "name": "About Us Styles",
  "settings": []
}
{% endschema %}
