{{ 'section-about-us.css' | asset_url | stylesheet_tag }}

<script>
// Professional About Us Page Enhancement
document.addEventListener('DOMContentLoaded', function() {
  // Add classes to existing rich text sections for better styling
  const richTextSections = document.querySelectorAll('.rich-text');

  richTextSections.forEach(function(section, index) {
    if (index === 0) {
      section.classList.add('about-hero');
      // Style the subtitle
      const subtitle = section.querySelector('p');
      if (subtitle) {
        subtitle.classList.add('hero-subtitle');
      }
    } else if (index === 1) {
      section.classList.add('about-content');

      // Style text paragraphs
      const paragraphs = section.querySelectorAll('p');
      paragraphs.forEach(function(p) {
        if (!p.querySelector('strong')) {
          p.classList.add('about-text');
        }
      });

      // Create services grid
      const servicesText = section.querySelector('div');
      if (servicesText && servicesText.innerHTML.includes('Cultural Adventures')) {
        servicesText.classList.add('services-grid');
        const servicesDivs = servicesText.querySelectorAll('div');
        servicesDivs.forEach(function(div, i) {
          div.classList.add('service-card');
          if (i === 0) div.classList.add('cultural');
          if (i === 1) div.classList.add('retreat');
          if (i === 2) div.classList.add('expedition');
        });
      }

      // Create stats grid
      const statsText = section.querySelector('div');
      if (statsText && statsText.innerHTML.includes('Happy Travelers')) {
        statsText.classList.add('stats-grid');
        const statsDivs = statsText.querySelectorAll('div');
        statsDivs.forEach(function(div) {
          div.classList.add('stat-item');
          const strong = div.querySelector('strong');
          if (strong) {
            strong.classList.add('stat-number');
            const text = div.childNodes[1];
            if (text) {
              const label = document.createElement('div');
              label.classList.add('stat-label');
              label.textContent = text.textContent.trim();
              div.appendChild(label);
              text.remove();
            }
          }
        });
      }

      // Style CTA
      const ctaP = section.querySelector('p strong');
      if (ctaP && ctaP.textContent.includes('Your journey of discovery')) {
        ctaP.parentElement.classList.add('cta-section');
      }
    }
  });

  // Add professional styling to contact form section
  const contactSection = document.querySelector('.contact-form');
  if (contactSection) {
    contactSection.classList.add('contact-form-section');
  }

  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        entry.target.classList.add('revealed');
      }
    });
  }, observerOptions);

  // Add scroll reveal to elements
  const elementsToReveal = document.querySelectorAll('.about-content h1, .about-content p, .services-grid, .service-card, .stats-grid');
  elementsToReveal.forEach(function(el) {
    el.classList.add('scroll-reveal');
    observer.observe(el);
  });

  // Animate statistics numbers when they come into view
  const statNumbers = document.querySelectorAll('.stat-number');
  statNumbers.forEach(function(statEl) {
    const observer = new IntersectionObserver(function(entries) {
      entries.forEach(function(entry) {
        if (entry.isIntersecting) {
          animateNumber(entry.target);
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.5 });

    observer.observe(statEl);
  });

  // Number animation function
  function animateNumber(element) {
    const finalNumber = element.textContent;
    const isPercentage = finalNumber.includes('%');
    const isPlusSign = finalNumber.includes('+');
    const numericValue = parseInt(finalNumber.replace(/[^\d]/g, ''));

    let currentNumber = 0;
    const increment = numericValue / 50; // 50 steps for smooth animation

    const timer = setInterval(function() {
      currentNumber += increment;
      if (currentNumber >= numericValue) {
        currentNumber = numericValue;
        clearInterval(timer);
      }

      let displayValue = Math.floor(currentNumber).toString();
      if (isPlusSign) displayValue += '+';
      if (isPercentage) displayValue += '%';

      element.textContent = displayValue;
    }, 30);
  }

  // Add click handlers to service cards for better UX
  const serviceCards = document.querySelectorAll('.service-card');
  serviceCards.forEach(function(card) {
    card.addEventListener('click', function() {
      // Add a subtle click effect
      this.style.transform = 'scale(0.98)';
      setTimeout(() => {
        this.style.transform = '';
      }, 150);
    });

    // Add keyboard accessibility
    card.setAttribute('tabindex', '0');
    card.addEventListener('keydown', function(e) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        this.click();
      }
    });
  });

  // Smooth scrolling for any internal links
  const internalLinks = document.querySelectorAll('a[href^="#"]');
  internalLinks.forEach(function(link) {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
});
</script>

{% schema %}
{
  "name": "About Us Styles",
  "settings": []
}
{% endschema %}
