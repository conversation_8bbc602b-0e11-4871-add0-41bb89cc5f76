/* About Us Page Professional Styling */

.about-hero {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 4rem 0;
  text-align: center;
}

.about-hero h1 {
  font-size: 3.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.about-hero h2 {
  font-size: 1.5rem;
  font-weight: 300;
  color: #7f8c8d;
  margin-bottom: 0;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 300;
  color: #7f8c8d;
  margin-bottom: 0;
  text-align: center;
}

.about-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.about-section {
  margin-bottom: 4rem;
}

.about-section h1,
.about-section h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.about-section h1::after,
.about-section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  border-radius: 2px;
}

.about-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #34495e;
  text-align: justify;
  margin-bottom: 2rem;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.service-card {
  background: #ffffff;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border-left: 4px solid;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--card-color), rgba(255,255,255,0));
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.service-card.cultural {
  border-left-color: #3498db;
  --card-color: #3498db;
}

.service-card.retreat {
  border-left-color: #2ecc71;
  --card-color: #2ecc71;
}

.service-card.expedition {
  border-left-color: #f39c12;
  --card-color: #f39c12;
}

.service-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.service-icon svg {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.service-card:hover .service-icon svg {
  transform: scale(1.1);
}

.service-card h3 {
  font-size: 1.4rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
}

.service-card p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem;
  border-radius: 15px;
  text-align: center;
  margin: 4rem 0;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

/* Statistics Section */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
  text-align: center;
}

.stat-item {
  background: #ffffff;
  padding: 2rem 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1.1rem;
  color: #7f8c8d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Inline stats styling */
.stats-inline {
  text-align: center;
  font-size: 1.2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 2rem 0;
  border-left: 4px solid #3498db;
}

.stats-inline strong {
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 700;
}

.cta-section p {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  position: relative;
  z-index: 1;
}

.contact-form-section {
  background: #f8f9fa;
  padding: 4rem 0;
  margin-top: 4rem;
}

.contact-form-section h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 2rem;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .about-hero h1 {
    font-size: 2.5rem;
  }

  .about-hero h2 {
    font-size: 1.2rem;
  }

  .about-section h1,
  .about-section h2 {
    font-size: 2rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-card {
    padding: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .cta-section {
    padding: 2rem;
    margin: 2rem 0;
  }

  .cta-section p {
    font-size: 1.1rem;
  }
}

@media screen and (max-width: 480px) {
  .about-content {
    padding: 0 1rem;
  }

  .about-hero {
    padding: 2rem 0;
  }

  .about-hero h1 {
    font-size: 2rem;
  }

  .about-section {
    margin-bottom: 2rem;
  }

  .about-text {
    font-size: 1rem;
    text-align: left;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-item {
    padding: 1.5rem 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .service-icon {
    width: 40px;
    height: 40px;
  }

  .service-icon svg {
    width: 20px;
    height: 20px;
  }
}

/* Animation for scroll reveal */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered animation delays */
.scroll-reveal:nth-child(1) { transition-delay: 0.1s; }
.scroll-reveal:nth-child(2) { transition-delay: 0.2s; }
.scroll-reveal:nth-child(3) { transition-delay: 0.3s; }
.scroll-reveal:nth-child(4) { transition-delay: 0.4s; }
.scroll-reveal:nth-child(5) { transition-delay: 0.5s; }

/* Hover effects for better interactivity */
.about-section h1:hover::after {
  width: 80px;
  transition: width 0.3s ease;
}

/* Enhanced service card interactions */
.service-card {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.service-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.service-card:hover::after {
  left: 100%;
}

/* Pulse animation for CTA section */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.cta-section:hover {
  animation: pulse 2s infinite;
}

/* Professional typography improvements */
.about-content h1,
.about-content h2,
.about-content h3 {
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.about-content p {
  font-family: Georgia, 'Times New Roman', serif;
}

/* Enhanced focus states for accessibility */
.service-card:focus-within {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .cta-section,
  .contact-form-section {
    display: none;
  }
  
  .service-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
