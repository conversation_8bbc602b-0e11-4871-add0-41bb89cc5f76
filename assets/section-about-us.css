/* About Us Page Professional Styling */

.about-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 6rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.about-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.about-hero h1 {
  font-size: 4rem;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  position: relative;
  z-index: 2;
  letter-spacing: -0.02em;
}

.about-hero h2 {
  font-size: 1.5rem;
  font-weight: 300;
  color: rgba(255,255,255,0.9);
  margin-bottom: 0;
  position: relative;
  z-index: 2;
}

.hero-subtitle {
  font-size: 1.3rem;
  font-weight: 300;
  color: rgba(255,255,255,0.9);
  margin: 2rem 0 0 0;
  text-align: center;
  position: relative;
  z-index: 2;
  font-style: italic;
}

.professional-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;
}

.about-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.about-section {
  margin-bottom: 4rem;
}

.section-heading {
  font-size: 2.8rem;
  color: #2c3e50;
  margin: 4rem 0 2rem 0;
  position: relative;
  padding-bottom: 1rem;
  font-weight: 700;
  text-align: center;
}

.section-heading::before {
  content: attr(data-section);
  position: absolute;
  top: -1rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1rem;
  color: #667eea;
  font-weight: 600;
  letter-spacing: 0.2em;
  text-transform: uppercase;
}

.section-heading::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
}

.service-heading {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.service-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  color: white;
  font-weight: normal;
}

.about-text {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #4a5568;
  text-align: justify;
  margin: 2rem 0;
  font-weight: 400;
  letter-spacing: 0.01em;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.service-card {
  background: #ffffff;
  padding: 2.5rem;
  margin: 2rem 0;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.1);
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, var(--card-color, #667eea), var(--card-color-end, #764ba2));
}

.service-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.service-card.cultural {
  --card-color: #667eea;
  --card-color-end: #764ba2;
}

.service-card.retreat {
  --card-color: #2ecc71;
  --card-color-end: #27ae60;
}

.service-card.expedition {
  --card-color: #f39c12;
  --card-color-end: #e67e22;
}

.service-card p {
  color: #5a6c7d;
  line-height: 1.8;
  font-size: 1.1rem;
  margin: 1.5rem 0 0 0;
}

.service-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.service-icon svg {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.service-card:hover .service-icon svg {
  transform: scale(1.1);
}

.service-card h3 {
  font-size: 1.4rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
}

.service-card p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem;
  border-radius: 15px;
  text-align: center;
  margin: 4rem 0;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

/* Statistics Section */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2.5rem;
  margin: 5rem 0;
  padding: 3rem 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  position: relative;
}

.stats-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="dots" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="2" fill="rgba(102,126,234,0.1)"/></pattern></defs><rect width="60" height="60" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
}

.stat-item {
  text-align: center;
  position: relative;
  z-index: 2;
  padding: 2rem 1rem;
  background: rgba(255,255,255,0.8);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.4s ease;
}

.stat-item:hover {
  transform: translateY(-8px) scale(1.05);
  background: rgba(255,255,255,0.95);
  box-shadow: 0 15px 40px rgba(102,126,234,0.2);
}

.stat-number {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  color: #5a6c7d;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 0.5rem;
}

/* Inline stats styling */
.stats-inline {
  text-align: center;
  font-size: 1.2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 2rem 0;
  border-left: 4px solid #3498db;
}

.stats-inline strong {
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 700;
}

.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 3rem;
  border-radius: 25px;
  text-align: center;
  margin: 5rem 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
}

.cta-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: shimmer 4s ease-in-out infinite;
}

.cta-section p {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 2rem 0;
  position: relative;
  z-index: 2;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.cta-button {
  display: inline-block;
  background: rgba(255,255,255,0.2);
  color: white;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  border: 2px solid rgba(255,255,255,0.3);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(10px);
}

.cta-button:hover {
  background: rgba(255,255,255,0.3);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.contact-form-section {
  background: #f8f9fa;
  padding: 4rem 0;
  margin-top: 4rem;
}

.contact-form-section h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 2rem;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .about-hero h1 {
    font-size: 2.5rem;
  }

  .about-hero h2 {
    font-size: 1.2rem;
  }

  .about-section h1,
  .about-section h2 {
    font-size: 2rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-card {
    padding: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .cta-section {
    padding: 2rem;
    margin: 2rem 0;
  }

  .cta-section p {
    font-size: 1.1rem;
  }
}

@media screen and (max-width: 480px) {
  .about-content {
    padding: 0 1rem;
  }

  .about-hero {
    padding: 2rem 0;
  }

  .about-hero h1 {
    font-size: 2rem;
  }

  .about-section {
    margin-bottom: 2rem;
  }

  .about-text {
    font-size: 1rem;
    text-align: left;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-item {
    padding: 1.5rem 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .service-icon {
    width: 40px;
    height: 40px;
  }

  .service-icon svg {
    width: 20px;
    height: 20px;
  }
}

/* Animation for scroll reveal */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered animation delays */
.scroll-reveal:nth-child(1) { transition-delay: 0.1s; }
.scroll-reveal:nth-child(2) { transition-delay: 0.2s; }
.scroll-reveal:nth-child(3) { transition-delay: 0.3s; }
.scroll-reveal:nth-child(4) { transition-delay: 0.4s; }
.scroll-reveal:nth-child(5) { transition-delay: 0.5s; }

/* Hover effects for better interactivity */
.about-section h1:hover::after {
  width: 80px;
  transition: width 0.3s ease;
}

/* Enhanced service card interactions */
.service-card {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.service-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.service-card:hover::after {
  left: 100%;
}

/* Pulse animation for CTA section */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.cta-section:hover {
  animation: pulse 2s infinite;
}

/* Professional typography improvements */
.about-content h1,
.about-content h2,
.about-content h3 {
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.about-content p {
  font-family: Georgia, 'Times New Roman', serif;
}

/* Enhanced focus states for accessibility */
.service-card:focus-within {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .cta-section,
  .contact-form-section {
    display: none;
  }
  
  .service-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
